from __future__ import annotations

import asyncio
import dataclasses
import json
import uuid
import os
import base64
import weakref
import numpy as np
from dataclasses import dataclass, field
from typing import Any, List, Literal, Optional

import aiohttp
from livekit import rtc
from livekit.agents import (
    DEFAULT_API_CONNECT_OPTIONS,
    APIConnectionError,
    APIConnectOptions,
    APIStatusError,
    APITimeoutError,
    tokenize,
    tts,
    utils,
)

from .log import logger
from .models import TTSEncoding, OrpheusTTSModels, OrpheusVoices

# Audio processing imports for high-quality resampling
try:
    import scipy.signal
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    logger.warning("scipy not available - using basic resampling")

# Voice-specific API configurations
# FIXED: Correct URLs and endpoints based on user examples and Baseten documentation
VOICE_CONFIGS = {
    "tara": {
        "api_url": "https://model-4w7jnzyw.api.baseten.co/environments/production/predict",  # FIXED: Correct Tara URL and endpoint
        "api_key": "k6orXdFV.GSget2Nism1txAP79bWTnL8p2gHDN5rw",
        "is_streaming": True,  # Streaming mode
        "api_voice_name": "tara"  # FIXED: Lowercase for API
    },
    "elise": {
        "api_url": "https://model-5qenjjpq.api.baseten.co/environments/production/predict",  # FIXED: Correct Elise URL and endpoint
        "api_key": "k6orXdFV.GSget2Nism1txAP79bWTnL8p2gHDN5rw",
        "is_streaming": True,  # Streaming mode
        "api_voice_name": "elise"  # FIXED: Lowercase for API consistency
    },
    "tara_async": {
        "api_url": "https://model-4w7jnzyw.api.baseten.co/environments/production/predict",  # FIXED: Use synchronous endpoint like working version
        "api_key": "k6orXdFV.GSget2Nism1txAP79bWTnL8p2gHDN5rw",
        "is_streaming": True,  # FIXED: Use streaming mode like working version  
        "api_voice_name": "tara"  # FIXED: Lowercase for API, base name without _async
    },
    "elise_async": {
        "api_url": "https://model-5qenjjpq.api.baseten.co/environments/production/predict",  # FIXED: Use synchronous endpoint like working version
        "api_key": "k6orXdFV.GSget2Nism1txAP79bWTnL8p2gHDN5rw",
        "is_streaming": True,  # FIXED: Use streaming mode like working version
        "api_voice_name": "elise"  # FIXED: Lowercase for API consistency, base name without _async
    }
}

# Default configuration (Tara)
DEFAULT_API_URL = VOICE_CONFIGS["tara"]["api_url"]
DEFAULT_API_KEY = VOICE_CONFIGS["tara"]["api_key"]

def get_voice_config(voice_id: str):
    """Get API configuration for a specific voice"""
    voice_id_lower = voice_id.lower()
    if voice_id_lower in VOICE_CONFIGS:
        return VOICE_CONFIGS[voice_id_lower]
    else:
        # Default to Tara if voice not found
        logger.warning(f"Voice '{voice_id}' not found, defaulting to Tara")
        return VOICE_CONFIGS["tara"]

@dataclass
class VoiceSettings:
    # Placeholder for future voice settings
    pass

@dataclass
class Voice:
    id: str
    name: str
    settings: VoiceSettings | None = None

# Available voices
AVAILABLE_VOICES = [
    Voice(id="tara", name="Tara", settings=None),
    Voice(id="elise", name="Elise", settings=None),
    Voice(id="tara_async", name="Tara (Backup)", settings=None),
    Voice(id="elise_async", name="Elise (Backup)", settings=None),
]

# Default voice for Orpheus TTS
DEFAULT_VOICE = AVAILABLE_VOICES[0]  # Tara

def resample_audio_24k_to_48k(audio_data: bytes) -> bytes:
    """
    High-quality resampling from 24kHz to 48kHz to fix LiveKit compatibility.

    This addresses the core audio artifacts issue by properly converting
    Orpheus TTS output (24kHz) to LiveKit's preferred format (48kHz).
    """
    try:
        if not SCIPY_AVAILABLE:
            # Fallback: simple duplication (not ideal but prevents artifacts)
            logger.warning("Using basic resampling - install scipy for better quality")
            # Simple 2x upsampling by duplicating samples
            samples = np.frombuffer(audio_data, dtype=np.int16)
            upsampled = np.repeat(samples, 2)
            return upsampled.tobytes()

        # AGGRESSIVE FIX: High-quality resampling with anti-aliasing
        samples = np.frombuffer(audio_data, dtype=np.int16)

        # Pre-filter to reduce resampling artifacts
        if len(samples) > 4:
            filtered_samples = samples.copy().astype(np.float32)
            # Apply gentle smoothing to reduce high-frequency content
            for i in range(1, len(filtered_samples) - 1):
                filtered_samples[i] = 0.25 * filtered_samples[i-1] + 0.5 * filtered_samples[i] + 0.25 * filtered_samples[i+1]
            samples = filtered_samples.astype(np.int16)

        # Resample from 24kHz to 48kHz (2x upsampling)
        resampled = scipy.signal.resample(samples, len(samples) * 2)

        # Post-resampling smoothing to eliminate artifacts
        if len(resampled) > 4:
            for i in range(1, len(resampled) - 1):
                resampled[i] = 0.25 * resampled[i-1] + 0.5 * resampled[i] + 0.25 * resampled[i+1]

        # Convert back to int16 and ensure proper range
        resampled = np.clip(resampled, -32768, 32767).astype(np.int16)

        logger.debug(f"🔄 High-quality resampled audio: {len(samples)} -> {len(resampled)} samples (24kHz -> 48kHz)")
        return resampled.tobytes()

    except Exception as e:
        logger.error(f"❌ Resampling failed: {e}, using original audio")
        return audio_data

def prevent_loose_cable_crackling(audio_data: bytes, sample_rate: int = 48000) -> bytes:
    """
    CRITICAL FIX: Prevent "loose cable" crackling artifacts with simple, robust processing.

    This addresses the specific crackling/popping sounds that occur when audio buffers
    have discontinuities - similar to loose speaker/mic cable connection sounds.
    """
    try:
        if len(audio_data) < 4:  # Need at least 2 samples
            return audio_data

        # Convert to numpy array for processing - CRITICAL FIX: Make writable copy
        samples = np.frombuffer(audio_data, dtype=np.int16).copy()  # .copy() makes it writable

        # AGGRESSIVE FIX: More thorough anti-crackling processing
        # Use longer fade and additional smoothing for stubborn artifacts
        fade_samples = min(240, len(samples) // 10)  # 5ms fade at 48kHz, or 10% of audio

        if len(samples) > fade_samples * 2 and fade_samples > 0:
            # Apply smooth exponential fade curves (better than linear)
            for i in range(fade_samples):
                # Exponential fade in (smoother than linear)
                fade_factor = (i / fade_samples) ** 0.5  # Square root for gentler curve
                samples[i] = int(samples[i] * fade_factor)

                # Exponential fade out
                fade_out_idx = len(samples) - 1 - i
                samples[fade_out_idx] = int(samples[fade_out_idx] * fade_factor)

        # ADDITIONAL FIX: Apply gentle low-pass filtering to remove high-frequency artifacts
        # Simple 3-point moving average to smooth out sharp transitions
        if len(samples) > 4:
            for i in range(1, len(samples) - 1):
                # Gentle smoothing: 25% previous + 50% current + 25% next
                smoothed = int(0.25 * samples[i-1] + 0.5 * samples[i] + 0.25 * samples[i+1])
                samples[i] = smoothed

        logger.debug(f"🎵 Applied simple anti-crackling fade: {fade_samples} samples")
        return samples.tobytes()

    except Exception as e:
        logger.error(f"❌ Anti-crackling processing failed: {e}")
        # FALLBACK: Apply absolute minimal processing
        try:
            samples = np.frombuffer(audio_data, dtype=np.int16).copy()
            if len(samples) > 4:
                # Just reduce the first and last few samples to prevent pops
                samples[0] = int(samples[0] * 0.5)
                samples[1] = int(samples[1] * 0.8)
                samples[-2] = int(samples[-2] * 0.8)
                samples[-1] = int(samples[-1] * 0.5)
                logger.debug("🔧 Applied fallback anti-pop processing")
                return samples.tobytes()
        except Exception as fallback_error:
            logger.error(f"❌ Fallback processing also failed: {fallback_error}")

        # Last resort: return original audio
        return audio_data

def add_silence_padding(audio_data: bytes, padding_ms: int = 100, sample_rate: int = 48000) -> bytes:
    """
    Add silence padding to prevent word cutting at start/end of audio.

    Args:
        audio_data: Raw PCM audio data
        padding_ms: Milliseconds of silence to add at start and end
        sample_rate: Sample rate of the audio
    """
    try:
        # Calculate silence samples needed
        silence_samples = int((padding_ms / 1000.0) * sample_rate)
        silence_bytes = b'\x00\x00' * silence_samples  # 16-bit silence

        # Add padding at start and end
        padded_audio = silence_bytes + audio_data + silence_bytes

        logger.debug(f"🔇 Added {padding_ms}ms silence padding: {len(audio_data)} -> {len(padded_audio)} bytes")
        return padded_audio

    except Exception as e:
        logger.error(f"❌ Silence padding failed: {e}")
        return audio_data

def calculate_text_threshold_from_bytes(chunk_size_bytes: int) -> int:
    """
    Convert chunk size in bytes to text synthesis threshold in characters.

    CRITICAL FIX: Conservative thresholds to prevent word cutting and ensure complete phrases.
    Larger chunks = fewer synthesis calls = better audio continuity and complete words.

    This provides conservative chunking for complete speech:
    - 1024 bytes (conservative) → ~400 characters (complete sentences + buffer)
    - 4096 bytes (balanced) → ~600 characters (multiple complete sentences)
    - 8192 bytes (aggressive) → ~900 characters (paragraph chunks with safety)

    Conservative approach ensures words are never cut mid-synthesis.
    """
    # CRITICAL FIX: Even more conservative thresholds to prevent word cutting
    # Prioritize complete words/phrases over speed
    if chunk_size_bytes <= 1024:
        return 400   # Conservative: wait for complete sentences + buffer
    elif chunk_size_bytes <= 4096:
        return 600   # Balanced: INCREASED further for complete thoughts
    elif chunk_size_bytes <= 8192:
        return 900   # Aggressive: INCREASED for complete passages
    else:
        return 1200  # Large: INCREASED for maximum word completeness

@dataclass
class OrpheusTTSOptions:
    api_key: str = DEFAULT_API_KEY
    api_url: str = DEFAULT_API_URL
    voice: Voice = field(default_factory=lambda: DEFAULT_VOICE)
    model: OrpheusTTSModels = "orpheus"
    encoding: TTSEncoding = "pcm_24000"
    sample_rate: int = 48000  # FIXED: Use 48kHz for LiveKit compatibility
    num_channels: int = 1
    max_tokens: int = 10000
    chunk_size_bytes: int = 4096  # Default to balanced preset (4096 bytes)
    text_synthesis_threshold: int = 0  # Will be calculated from chunk_size_bytes
    enable_resampling: bool = True  # NEW: Enable 24kHz -> 48kHz resampling
    add_silence_padding: bool = True  # NEW: Add silence padding to prevent cutting
    word_tokenizer: tokenize.WordTokenizer = field(
        default_factory=lambda: tokenize.basic.WordTokenizer(ignore_punctuation=False)
    )

class OrpheusTTS(tts.TTS):
    def __init__(
        self,
        *,
        api_key: str | None = None,
        api_url: str | None = None,
        voice: Voice = DEFAULT_VOICE,
        model: OrpheusTTSModels = "orpheus",
        encoding: TTSEncoding = "pcm_24000",
        chunk_size_bytes: int = 4096,  # NEW: Accept chunk size configuration
        word_tokenizer: tokenize.WordTokenizer = tokenize.basic.WordTokenizer(ignore_punctuation=False),
        http_session: aiohttp.ClientSession | None = None,
    ) -> None:
        """
        Create a new instance of Orpheus TTS.
        
        Args:
            api_key: The API key for Baseten. If not provided, will use voice-specific API key.
            api_url: The API URL for the Baseten model. If not provided, will use voice-specific URL.
            voice: The voice to use for synthesis.
            model: The Orpheus model to use.
            encoding: The audio encoding.
            chunk_size_bytes: Size of audio chunks in bytes (affects text synthesis timing).
            word_tokenizer: The tokenizer to use for words.
            http_session: The HTTP session to use for requests.
        """
        # Get voice-specific configuration
        voice_config = get_voice_config(voice.id)
        
        # Use voice-specific settings if not explicitly provided
        if api_key is None:
            api_key = voice_config["api_key"]
        if api_url is None:
            api_url = voice_config["api_url"]
            
        # Store voice configuration info
        self._voice_config = voice_config
        self._is_streaming = voice_config["is_streaming"]
        
        logger.info(f"🎤 Initializing OrpheusTTS with voice: {voice.name} ({voice.id})")
        logger.info(f"🔄 Streaming mode: {'✅ ENABLED' if self._is_streaming else '❌ DISABLED (Non-streaming)'}")
        logger.info(f"🌐 API endpoint: {api_url}")
        logger.info(f"🔑 API key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else api_key}")
        logger.info(f"🎯 Voice config: {voice_config}")
        
        # Calculate text synthesis threshold from chunk size
        text_synthesis_threshold = calculate_text_threshold_from_bytes(chunk_size_bytes)
        
        # Initialize parent class with TTS capabilities
        super().__init__(
            capabilities=tts.TTSCapabilities(
                streaming=self._is_streaming,  # Use voice-specific streaming capability
            ),
            sample_rate=48000,  # FIXED: Use 48kHz for LiveKit compatibility
            num_channels=1,  # Mono audio
        )

        self._opts = OrpheusTTSOptions(
            api_key=api_key,
            api_url=api_url,
            voice=voice,
            model=model,
            encoding=encoding,
            chunk_size_bytes=chunk_size_bytes,
            text_synthesis_threshold=text_synthesis_threshold,
            word_tokenizer=word_tokenizer,
        )
        self._session = http_session
        self._streams = weakref.WeakSet()  # Track active streams

        logger.info(f"🎯 Chunk size: {chunk_size_bytes} bytes → Text threshold: {text_synthesis_threshold} characters")

    async def _ensure_session(self) -> aiohttp.ClientSession:
        if not self._session:
            self._session = await _connection_pool.get_session()
        return self._session
    
    def prewarm(self) -> None:
        """Pre-warm connection pool for better performance"""
        logger.info("🔥 Pre-warming connection pool for Orpheus TTS")
        # This will be called by the agent to prepare connections
        pass
    
    async def aclose(self) -> None:
        """Close all active streams and connections"""
        for stream in list(self._streams):
            await stream.aclose()
        self._streams.clear()
        
        # Note: We don't close the global connection pool here
        # as it might be used by other TTS instances

    async def list_voices(self) -> List[Voice]:
        """Return list of available voices"""
        return AVAILABLE_VOICES

    def update_options(
        self,
        *,
        voice: Voice | None = None,
        model: OrpheusTTSModels | None = None,
    ) -> None:
        """
        Update the TTS options.
        
        Args:
            voice: The new voice to use.
            model: The new model to use.
        """
        if voice:
            self._opts.voice = voice
            # Update API configuration based on new voice
            voice_config = get_voice_config(voice.id)
            self._opts.api_key = voice_config["api_key"]
            self._opts.api_url = voice_config["api_url"]
            logger.info(f"Updated voice to {voice.id} with API URL: {self._opts.api_url}")
        if model:
            self._opts.model = model

    def synthesize(
        self,
        text: str,
        *,
        conn_options: APIConnectOptions = DEFAULT_API_CONNECT_OPTIONS,
    ) -> "OrpheusSynthesizeStream":
        """
        Synthesize speech from text using Orpheus TTS.
        
        Args:
            text: The text to synthesize.
            conn_options: Connection options for the API.
            
        Returns:
            A stream of synthesized audio.
        """
        # Log the text being synthesized
        logger.info(f"Synthesizing text: '{text[:100]}{'...' if len(text) > 100 else ''}' with voice {self._opts.voice.id}")
        
        # Check if text is empty, but don't replace it with a default
        # unless it's completely empty
        if not text or text.strip() == "":
            logger.warning("Empty text provided for synthesis, using default")
            text = "Hello, welcome to Orpheus TTS."
            
        # Create stream and register it
        stream = OrpheusSynthesizeStream(
            tts=self,
            conn_options=conn_options,
            opts=self._opts,
            session=None,  # Will be set async
            input_text=text,
        )
        self._streams.add(stream)
        return stream
        
    def stream(self) -> tts.SynthesizeStream:
        """
        Create a streaming synthesis session.
        
        This method is required by the LiveKit Agents framework for streaming TTS.
        
        Returns:
            A stream for synthesizing speech.
        """
        # When the agent pipeline requests a stream, it will later call `stream.write()` with the text.
        # But in some agent pipelines, a stream is created but text is never passed via write.
        # So we need to intercept the LLM response a different way.
        logger.info("Creating new streaming synthesis session for Orpheus TTS - using StreamingTTSBridge")
        stream = StreamingTTSBridge(
            tts=self,
            conn_options=DEFAULT_API_CONNECT_OPTIONS,
            session=None,  # Will be set async
            opts=self._opts,
        )
        self._streams.add(stream)
        return stream

# A bridge class to connect the agent pipeline's way of providing text with our TTS system
class StreamingTTSBridge(tts.ChunkedStream):
    """
    Smart streaming TTS bridge that handles text accumulation, intelligent chunking,
    and graceful shutdown without task destruction errors.
    """
    
    def __init__(
        self,
        *,
        tts: OrpheusTTS,
        opts: OrpheusTTSOptions,
        conn_options: APIConnectOptions,
        session: aiohttp.ClientSession,
    ) -> None:
        super().__init__(tts=tts, input_text="")
        self._conn_options = conn_options
        self._opts = opts
        self._session = session
        
        # Voice configuration
        self._is_streaming = tts._is_streaming
        self._voice_config = tts._voice_config
        
        # Text management with improved buffering
        self._buffer = ""
        self._input_ended = False
        self._processing_complete = False
        self._last_synthesis_time = 0
        
        # Synchronization
        self._text_ready = asyncio.Event()
        self._shutdown_event = asyncio.Event()
        
        # Task management
        self._main_task = None
        self._is_running = False
        
        # FIXED: Reduce logging noise - only log significant events
        self._char_count_since_log = 0
        self._log_interval = 50  # Log every 50 characters instead of every character
        
        logger.info("🎯 Initialized smart StreamingTTSBridge")
        
        # Validate event channel
        if hasattr(self, '_event_ch') and self._event_ch:
            logger.info(f"✅ Event channel confirmed: {type(self._event_ch)}")
        else:
            logger.error("❌ Event channel not available!")
    
    def push_text(self, text: str) -> None:
        """Add text to the synthesis buffer with reduced logging noise."""
        if self._input_ended:
            return
            
        if text:
            self._buffer += text
            self._char_count_since_log += len(text)
            
            # FIXED: Dramatically reduce logging noise - only log periodically
            if self._char_count_since_log >= self._log_interval:
                logger.debug(f"📝 Buffer update: +{self._char_count_since_log} chars, total: {len(self._buffer)} chars")
                self._char_count_since_log = 0
            
            # Signal that text is ready for processing
            self._text_ready.set()
    
    def end_input(self) -> None:
        """Mark input as complete."""
        logger.info("🏁 Input ended")
        self._input_ended = True
        self._text_ready.set()  # Wake up processor
    
    async def _run(self) -> None:
        """Main processing loop with smart error handling."""
        logger.info("🚀 Starting smart TTS processing")
        self._is_running = True
        
        try:
            # Start main processing task
            self._main_task = asyncio.create_task(self._process_text_smartly())
            await self._main_task
            
        except asyncio.CancelledError:
            logger.info("✅ Main task cancelled gracefully")
            raise  # Re-raise to properly handle cancellation
        except Exception as e:
            logger.error(f"❌ Error in main processing: {e}")
        finally:
            self._is_running = False
            self._processing_complete = True
            logger.info("✅ Smart TTS processing completed")
    
    async def _process_text_smartly(self) -> None:
        """Smart text processing with improved timing and sentence detection."""
        
        while not self._processing_complete:
            try:
                # Wait for text or shutdown signal with longer timeout
                done, pending = await asyncio.wait(
                    [
                        asyncio.create_task(self._text_ready.wait()),
                        asyncio.create_task(self._shutdown_event.wait())
                    ],
                    return_when=asyncio.FIRST_COMPLETED,
                    timeout=2.0  # INCREASED timeout to allow more text accumulation
                )
                
                # Cancel pending tasks
                for task in pending:
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                
                # Check for shutdown
                if self._shutdown_event.is_set():
                    logger.info("🛑 Shutdown signal received")
                    break
                
                # Process text if available
                if self._text_ready.is_set():
                    self._text_ready.clear()
                    
                    # CRITICAL FIX: Improved synthesis decision logic to prevent word cutting
                    buffer_length = len(self._buffer)
                    contains_sentence_end = any(self._buffer.rstrip().endswith(p) for p in ['.', '!', '?'])
                    contains_pause_marker = any(marker in self._buffer for marker in [', ', '; ', ': ', ' - ', ' and ', ' but ', ' or '])

                    # FIXED: More conservative conditions to ensure complete words/phrases
                    should_process = (
                        self._input_ended or  # Input complete - always synthesize remaining
                        buffer_length > 1000 or  # Buffer very large - prevent memory issues
                        (buffer_length > 300 and contains_sentence_end) or  # Complete sentence with good length
                        (buffer_length > 400 and contains_pause_marker) or  # Long phrase with natural pause
                        (buffer_length > 600)  # Moderately long text - but wait longer for completeness
                    )

                    # FIXED: Longer timing constraint to prevent word cutting
                    import time
                    current_time = time.time()
                    time_since_last = current_time - self._last_synthesis_time
                    min_synthesis_interval = 2.0  # INCREASED: Minimum 2 seconds between synthesis calls
                    
                    if should_process and self._buffer.strip():
                        if time_since_last >= min_synthesis_interval or self._input_ended:
                            await self._synthesize_buffer()
                            self._last_synthesis_time = current_time
                        else:
                            # Wait a bit more before synthesis
                            logger.debug(f"⏱️ Delaying synthesis: {time_since_last:.1f}s < {min_synthesis_interval}s")
                
                # Exit if input ended and buffer empty
                if self._input_ended and not self._buffer.strip():
                    logger.info("✅ All text processed, exiting")
                    break
                    
            except asyncio.TimeoutError:
                # Timeout is normal, continue loop
                continue
            except asyncio.CancelledError:
                logger.info("✅ Text processor cancelled")
                break
            except Exception as e:
                logger.error(f"❌ Error in text processing: {e}")
                await asyncio.sleep(0.1)
    
    async def _synthesize_buffer(self) -> None:
        """Synthesize current buffer content with improved error handling."""
        if not self._buffer.strip():
            return
            
        text = self._buffer.strip()
        self._buffer = ""  # Clear buffer
        
        logger.info(f"🎵 Synthesizing: '{text[:100]}{'...' if len(text) > 100 else ''}' ({len(text)} chars)")
        
        try:
            # Add silence padding for first chunk to prevent cutting
            should_add_padding = not hasattr(self, '_first_synthesis_done')
            if should_add_padding:
                self._first_synthesis_done = True
                logger.info("🔇 Adding silence padding to prevent start cutting")

            # Directly synthesize using the TTS API without creating another stream
            await self._direct_synthesize(text, should_add_padding)
            
            # Smart delay: shorter for final synthesis, longer for streaming
            if self._input_ended:
                await asyncio.sleep(0.1)  # Shorter delay for final synthesis
            else:
                await asyncio.sleep(0.2)  # Moderate delay for streaming synthesis
            
        except Exception as e:
            logger.error(f"❌ Synthesis error: {e}")
    
    async def _direct_synthesize(self, text: str, should_add_padding: bool = False) -> None:
        """Directly synthesize text using the Orpheus API with high-quality audio processing."""
        # CRITICAL FIX: Create a fresh session for each synthesis to avoid executor shutdown
        try:
            # Always create a fresh session to avoid shutdown issues
            session = await self._tts._ensure_session()
        except Exception as e:
            logger.error(f"❌ Failed to get session: {e}")
            return

        # Generate a unique request ID
        request_id = str(uuid.uuid4())

        # Prepare the request payload
        api_voice_name = self._voice_config["api_voice_name"]

        if self._is_streaming:
            payload = {
                'voice': api_voice_name,
                'prompt': text,
                'max_tokens': self._opts.max_tokens
            }
        else:
            payload = {
                'model_input': {
                    'voice': api_voice_name,
                    'prompt': text,
                    'max_tokens': self._opts.max_tokens
                }
            }

        headers = {"Authorization": f"Api-Key {self._opts.api_key}"}
        headers.update({
            "Connection": "keep-alive",
            "Accept": "audio/wav, audio/*, */*",
            "User-Agent": "OrpheusTTS/1.0"
        })

        logger.info(f"🌐 Sending request to Orpheus API: {self._opts.api_url}")
        logger.info(f"🎤 Using voice: {api_voice_name} (from config: {self._voice_config['api_voice_name']})")
        logger.info(f"📦 Payload: {payload}")

        try:
            # FIXED: Add proper timeout and logging
            logger.info(f"🔄 Making direct synthesis request to {self._opts.api_url}")

            async with session.post(
                self._opts.api_url,
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=60.0)  # FIXED: Add explicit timeout
            ) as resp:
                logger.info(f"📡 Direct synthesis response: status={resp.status}")
                if resp.status != 200:
                    error_text = await resp.text()
                    logger.error(f"Orpheus TTS API error: {resp.status} - {error_text}")
                    return

                # CRITICAL FIX: High-quality audio processing to eliminate artifacts
                frame_count = 0
                total_bytes = 0
                accumulated_audio = b''

                # Process audio stream and accumulate for proper resampling
                async for chunk in resp.content.iter_chunked(8192):  # Larger chunks for stability
                    if chunk:
                        total_bytes += len(chunk)

                        # CRITICAL FIX: Skip WAV header cleanly without complex processing
                        if frame_count == 0 and chunk.startswith(b'RIFF'):
                            # Simple header removal - find data chunk
                            data_pos = chunk.find(b'data')
                            if data_pos != -1:
                                # Skip to actual audio data (data chunk header is 8 bytes)
                                audio_data = chunk[data_pos + 8:]
                                logger.debug(f"🎵 Skipped WAV header, audio data: {len(audio_data)} bytes")
                            else:
                                # No data chunk found, treat as raw audio
                                audio_data = chunk[44:] if len(chunk) > 44 else chunk
                                logger.debug("⚠️ No data chunk found, using fallback header skip")
                        else:
                            # Regular audio chunk - no processing
                            audio_data = chunk

                        # Accumulate audio data for batch processing
                        if audio_data:
                            accumulated_audio += audio_data
                            frame_count += 1

                # CRITICAL FIX: Process accumulated audio with high-quality resampling
                if accumulated_audio:
                    # Apply resampling from 24kHz to 48kHz if enabled
                    if self._opts.enable_resampling:
                        logger.info("🔄 Applying high-quality 24kHz -> 48kHz resampling")
                        processed_audio = resample_audio_24k_to_48k(accumulated_audio)
                        target_sample_rate = 48000
                    else:
                        processed_audio = accumulated_audio
                        target_sample_rate = 24000

                    # CRITICAL FIX: Apply anti-crackling processing to prevent "loose cable" artifacts
                    logger.info("🎵 Applying anti-crackling processing to prevent loose cable artifacts")
                    processed_audio = prevent_loose_cable_crackling(processed_audio, sample_rate=target_sample_rate)

                    # Add silence padding if enabled
                    if self._opts.add_silence_padding:
                        logger.info("🔇 Adding silence padding to prevent word cutting")
                        processed_audio = add_silence_padding(processed_audio, padding_ms=100, sample_rate=target_sample_rate)

                    # Create audio frame with processed data
                    samples_per_channel = len(processed_audio) // 2  # 16-bit samples
                    if samples_per_channel > 0:
                        # CRITICAL FIX: Ensure consistent sample rate (always 48kHz for LiveKit)
                        audio_frame = rtc.AudioFrame(
                            data=processed_audio,
                            sample_rate=48000,  # FIXED: Always use 48kHz for LiveKit compatibility
                            num_channels=1,
                            samples_per_channel=samples_per_channel
                        )
                        self._send_frame_safely(audio_frame, f"processed_{request_id}")

                logger.info(f"✅ Direct synthesis complete: {frame_count} chunks, {total_bytes} bytes processed, {len(processed_audio) if 'processed_audio' in locals() else 0} bytes output")

        except asyncio.TimeoutError as e:
            logger.error(f"❌ Direct synthesis timeout: {e}")
            # Try to provide some audio even if synthesis fails
            self._send_fallback_audio("timeout")
        except aiohttp.ClientError as e:
            logger.error(f"❌ Direct synthesis connection error: {e}")
            self._send_fallback_audio("connection_error")
        except Exception as e:
            logger.error(f"❌ Direct synthesis unexpected error: {e}")
            self._send_fallback_audio("unexpected_error")

    def _send_fallback_audio(self, error_type: str):
        """Send fallback audio when synthesis fails"""
        try:
            if hasattr(self, '_event_ch') and self._event_ch:
                # Create a simple silence frame as fallback
                silence_frame = self._create_silence_frame(duration_ms=500)
                if silence_frame:
                    self._send_frame_safely(silence_frame, f"fallback_{error_type}")
                    logger.info(f"📢 Sent fallback silence frame for {error_type}")
        except Exception as fallback_error:
            logger.error(f"❌ Fallback audio creation failed: {fallback_error}")
    
    def _create_silence_frame(self, duration_ms: int = 50) -> rtc.AudioFrame | None:
        """Create a simple silence frame without fade effects to prevent artifacts."""
        try:
            # FIXED: Calculate samples for 48kHz (LiveKit compatible)
            samples_per_ms = 48  # 48 samples per ms at 48kHz
            total_samples = duration_ms * samples_per_ms

            # FIXED: Create pure silence without fade effects to prevent artifacts
            # Simple zero-filled audio data
            silence_data = b'\x00\x00' * total_samples  # 16-bit zero samples

            return rtc.AudioFrame(
                data=silence_data,
                sample_rate=48000,  # FIXED: Use 48kHz for LiveKit compatibility
                num_channels=1,
                samples_per_channel=total_samples
            )
        except Exception as e:
            logger.error(f"Failed to create silence frame: {e}")
            return None
    
    def _send_frame_safely(self, frame: rtc.AudioFrame, frame_id: str) -> None:
        """Send frame with error handling."""
        try:
            if hasattr(self, '_event_ch') and self._event_ch:
                self._event_ch.send_nowait(
                    tts.SynthesizedAudio(
                        request_id=frame_id,
                        frame=frame,
                    )
                )
            else:
                logger.error("❌ No event channel available for frame transmission")
        except Exception as e:
            logger.debug(f"Frame {frame_id} send failed: {e}")
    
    async def close(self) -> None:
        """Graceful shutdown with proper task cleanup."""
        logger.info("🔄 Closing StreamingTTSBridge")
        
        # Signal shutdown
        self._shutdown_event.set()
        self._processing_complete = True
        
        # Cancel main task with timeout
        if self._main_task and not self._main_task.done():
            logger.info("⏳ Cancelling main task...")
            self._main_task.cancel()
            
            try:
                await asyncio.wait_for(self._main_task, timeout=2.0)
                logger.info("✅ Main task cancelled successfully")
            except asyncio.TimeoutError:
                logger.warning("⚠️ Main task cancellation timed out")
            except asyncio.CancelledError:
                logger.info("✅ Main task cancelled")
            except Exception as e:
                logger.error(f"❌ Error during task cancellation: {e}")
        
        # Call parent cleanup
        await super().close()
        logger.info("✅ StreamingTTSBridge closed successfully")

class OrpheusSynthesizeStream(tts.ChunkedStream):
    """
    Stream that synthesizes speech using Orpheus TTS.
    Handles the streaming response from the Baseten API.
    """

    def __init__(
        self,
        *,
        tts: OrpheusTTS,
        opts: OrpheusTTSOptions,
        conn_options: APIConnectOptions,
        session: aiohttp.ClientSession,
        input_text: str = "",
    ) -> None:
        super().__init__(tts=tts, input_text=input_text, conn_options=conn_options)
        self._opts = opts
        self._session = session
        self._buffer = ""  # Buffer for text when using write method
        self._is_streaming_mode = input_text == ""  # True if created via stream() method
        
        # FIXED: Get streaming mode from parent TTS instance
        self._is_streaming = tts._is_streaming
        self._voice_config = tts._voice_config

        # CRITICAL FIX: Initialize buffer continuity tracking
        self._last_sample_value = 0
        


    async def _run(self, output_emitter: tts.AudioEmitter) -> None:
        """
        Run the synthesis stream with minimal audio processing to prevent artifacts.
        """
        # CRITICAL FIX: Always get a fresh session to avoid executor shutdown
        try:
            session = await self._tts._ensure_session()
        except Exception as e:
            logger.error(f"❌ Failed to get session for synthesis: {e}")
            return

        # Generate a unique request ID for this synthesis request
        request_id = str(uuid.uuid4())

        # CRITICAL FIX: Initialize output emitter with WebRTC-compatible settings
        output_emitter.initialize(
            request_id=request_id,
            sample_rate=48000,  # CRITICAL: Use 48kHz to match WebRTC standard and prevent browser mode switching
            num_channels=1,
            mime_type="audio/pcm",  # Use PCM instead of WAV to avoid format issues
            stream=True,
        )

        # Prepare the text to synthesize
        text_to_synthesize = self.input_text

        # Log the raw input text for debugging
        logger.info(f"Raw input text: '{text_to_synthesize}'")

        # Check for agent transcript in the text
        if "agent_transcript" in text_to_synthesize:
            try:
                # This is a special case where we've received JSON data with the transcript
                data = json.loads(text_to_synthesize)
                if "agent_transcript" in data:
                    text_to_synthesize = data["agent_transcript"]
                    logger.info(f"Extracted agent transcript: '{text_to_synthesize[:100]}{'...' if len(text_to_synthesize) > 100 else ''}'")
            except json.JSONDecodeError:
                # Not JSON, just use the text as is
                logger.warning("Failed to parse text as JSON, using raw text")

        # Only use default text if input is completely empty
        if not text_to_synthesize or text_to_synthesize.strip() == "":
            text_to_synthesize = "Hello, this is a test of the Orpheus Text to Speech system."
            logger.info(f"Using default text for synthesis: '{text_to_synthesize}'")
        else:
            # Make sure to use the actual input text
            logger.info(f"Synthesizing text: '{text_to_synthesize[:100]}{'...' if len(text_to_synthesize) > 100 else ''}'")

        # FIXED: Prepare the request payload based on streaming vs non-streaming mode
        # CRITICAL FIX: Use correct API voice name with proper case sensitivity
        api_voice_name = self._voice_config["api_voice_name"]

        if self._is_streaming:
            # Streaming mode: use direct payload format
            payload = {
                'voice': api_voice_name,  # FIXED: Use correct API voice name
                'prompt': text_to_synthesize,
                'max_tokens': self._opts.max_tokens
            }
            logger.info(f"📡 Using streaming mode payload format with voice: {api_voice_name}")
        else:
            # Non-streaming mode: wrap payload in model_input
            payload = {
                'model_input': {
                    'voice': api_voice_name,  # FIXED: Use correct API voice name
                    'prompt': text_to_synthesize,
                    'max_tokens': self._opts.max_tokens
                }
            }
            logger.info(f"📦 Using non-streaming mode payload format (wrapped in model_input) with voice: {api_voice_name}")

        # Prepare headers with API key
        headers = {"Authorization": f"Api-Key {self._opts.api_key}"}

        # FIXED: Add connection management headers for stable long audio synthesis
        headers.update({
            "Connection": "keep-alive",
            "Accept": "audio/wav, audio/*, */*",
            "User-Agent": "OrpheusTTS/1.0"
        })

        logger.info(f"Sending request to Orpheus TTS API. URL: {self._opts.api_url}")
        logger.info(f"Request details - Voice: {self._opts.voice.id}, Text length: {len(text_to_synthesize)} chars")
        logger.info(f"Sample text: {text_to_synthesize[:50]}...")

        try:
            # FIXED: Send request with proper timeout and error handling
            logger.info(f"🔄 Making HTTP request to {self._opts.api_url}")

            async with session.post(
                self._opts.api_url,
                headers=headers,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=60.0)  # FIXED: Add explicit timeout
            ) as resp:
                logger.info(f"📡 Received response: status={resp.status}, content-type={resp.headers.get('Content-Type', 'unknown')}")
                if resp.status != 200:
                    error_text = await resp.text()
                    logger.error(f"Orpheus TTS API error: {resp.status} - {error_text}")
                    raise APIStatusError(f"Orpheus TTS API returned status {resp.status}: {error_text}")

                # Log the content type for debugging
                content_type = resp.headers.get('Content-Type', 'unknown')
                logger.info(f"Received response with Content-Type: {content_type}")

                # CRITICAL FIX: High-quality audio processing with proper resampling
                frame_count = 0
                total_bytes_received = 0
                accumulated_audio = b''

                # Accumulate all audio data first for proper processing
                async for chunk in resp.content.iter_chunked(8192):  # Standard chunk size
                    if chunk:  # Only process if chunk has data
                        total_bytes_received += len(chunk)

                        # CRITICAL FIX: Simple WAV header removal without complex processing
                        if frame_count == 0 and chunk.startswith(b'RIFF'):
                            # Find the data chunk and skip to PCM data
                            data_pos = chunk.find(b'data')
                            if data_pos != -1:
                                # Skip data chunk header (8 bytes: 'data' + size)
                                pcm_data = chunk[data_pos + 8:]
                                logger.info(f"🎵 Skipped WAV header, PCM data: {len(pcm_data)} bytes")
                            else:
                                # Fallback: skip standard 44-byte header
                                pcm_data = chunk[44:] if len(chunk) > 44 else chunk
                                logger.info("⚠️ Used fallback header skip")
                        else:
                            # Regular PCM chunk
                            pcm_data = chunk

                        # Accumulate audio data for batch processing
                        if pcm_data:
                            accumulated_audio += pcm_data
                            frame_count += 1

                # CRITICAL FIX: Process accumulated audio with high-quality resampling
                if accumulated_audio:
                    # Apply resampling from 24kHz to 48kHz if enabled
                    if self._opts.enable_resampling:
                        logger.info("🔄 Applying high-quality 24kHz -> 48kHz resampling")
                        processed_audio = resample_audio_24k_to_48k(accumulated_audio)
                    else:
                        processed_audio = accumulated_audio

                    # CRITICAL FIX: Apply anti-crackling processing to prevent "loose cable" artifacts
                    logger.info("🎵 Applying anti-crackling processing to prevent loose cable artifacts")
                    processed_audio = prevent_loose_cable_crackling(processed_audio, sample_rate=48000)

                    # Add silence padding if enabled
                    if self._opts.add_silence_padding:
                        logger.info("🔇 Adding silence padding to prevent word cutting")
                        processed_audio = add_silence_padding(processed_audio, padding_ms=100, sample_rate=48000)

                    # CRITICAL FIX: Apply buffer continuity protection to prevent "loose cable" crackling
                    logger.info("🔧 Applying buffer continuity protection")
                    final_audio = self._ensure_buffer_continuity(processed_audio)

                    # FINAL FIX: Apply one more round of anti-crackling to catch any remaining artifacts
                    logger.info("🎵 Applying final anti-crackling pass")
                    ultra_smooth_audio = prevent_loose_cable_crackling(final_audio, sample_rate=48000)

                    # Push processed audio to output emitter
                    output_emitter.push(ultra_smooth_audio)

                # FIXED: Validate stream completion
                logger.info(f"✅ Stream complete: {frame_count} chunks, {total_bytes_received} bytes received, {len(processed_audio) if 'processed_audio' in locals() else 0} bytes output")

                # Flush the output emitter to ensure complete audio playback
                output_emitter.flush()

        except asyncio.TimeoutError as e:
            logger.error(f"❌ Orpheus TTS API timeout: {e}")
            raise APIConnectionError(f"Orpheus TTS API timeout: {e}")
        except aiohttp.ClientError as e:
            logger.error(f"❌ Orpheus TTS API connection error: {e}")
            raise APIConnectionError(f"Orpheus TTS API connection error: {e}")
        except Exception as e:
            logger.error(f"❌ Unexpected error in TTS synthesis: {e}")
            raise APIConnectionError(f"Unexpected TTS error: {e}")

    def _ensure_buffer_continuity(self, audio_data: bytes) -> bytes:
        """
        CRITICAL FIX: Ensure buffer continuity to prevent "loose cable" crackling artifacts.

        This prevents the crackling/popping sounds that occur when there are
        discontinuities between audio buffers - like loose cable connections.
        """
        try:
            if len(audio_data) < 2:
                return audio_data

            # Convert to samples - CRITICAL FIX: Make writable copy
            samples = np.frombuffer(audio_data, dtype=np.int16).copy()  # .copy() makes it writable

            # CRITICAL FIX: Smooth transition from last buffer to prevent discontinuity pops
            if len(samples) > 0:
                first_sample = samples[0]

                # AGGRESSIVE FIX: Lower threshold and longer ramp for better continuity
                sample_diff = abs(int(first_sample) - int(self._last_sample_value))
                if sample_diff > 500:  # Lower threshold to catch smaller discontinuities
                    # Create a longer, smoother ramp
                    ramp_length = min(48, len(samples) // 10)  # Longer ramp (1ms at 48kHz)
                    if ramp_length > 0:
                        # Use exponential ramp for smoother transition
                        t = np.linspace(0, 1, ramp_length)
                        smooth_curve = t ** 0.5  # Square root curve for gentle transition
                        ramp = self._last_sample_value + (first_sample - self._last_sample_value) * smooth_curve
                        samples[:ramp_length] = ramp.astype(np.int16)

                    logger.debug(f"🔧 Smoothed buffer discontinuity: {self._last_sample_value} -> {first_sample}")

                # Remember the last sample for next buffer
                self._last_sample_value = int(samples[-1])

            return samples.tobytes()

        except Exception as e:
            logger.error(f"❌ Buffer continuity fix failed: {e}")
            return audio_data

# Add connection pool management
class ConnectionPool:
    """Simple connection pool for HTTP sessions"""
    
    def __init__(self, max_connections: int = 10, max_session_duration: float = 300.0):
        self._max_connections = max_connections
        self._max_session_duration = max_session_duration
        self._sessions: List[aiohttp.ClientSession] = []
        self._session_created_times: List[float] = []
        self._lock = asyncio.Lock()
    
    async def get_session(self) -> aiohttp.ClientSession:
        """Get a fresh HTTP session with proper timeouts"""
        async with self._lock:
            import time
            current_time = time.time()

            # CRITICAL FIX: Always create fresh sessions with proper timeouts
            # Close any existing sessions first
            for session in self._sessions:
                try:
                    if not session.closed:
                        await session.close()
                except Exception as e:
                    pass  # Ignore close errors

            self._sessions.clear()
            self._session_created_times.clear()

            # Always create a new session with proper timeouts
            try:
                # FIXED: Add proper timeouts to prevent hanging
                timeout = aiohttp.ClientTimeout(
                    total=60.0,        # Total timeout: 60 seconds
                    connect=10.0,      # Connection timeout: 10 seconds
                    sock_read=30.0,    # Socket read timeout: 30 seconds
                    sock_connect=10.0  # Socket connect timeout: 10 seconds
                )

                connector = aiohttp.TCPConnector(
                    limit=100,
                    limit_per_host=30,
                    keepalive_timeout=30,
                    enable_cleanup_closed=True,
                    ttl_dns_cache=300,  # DNS cache TTL
                    use_dns_cache=True
                )

                session = aiohttp.ClientSession(
                    timeout=timeout,
                    connector=connector
                )

                self._sessions.append(session)
                self._session_created_times.append(current_time)

                logger.debug(f"✅ Created fresh HTTP session with 60s timeout")
                return session

            except Exception as e:
                logger.error(f"❌ Failed to create HTTP session: {e}")
                # Return a basic session with timeout as fallback
                return aiohttp.ClientSession(
                    timeout=aiohttp.ClientTimeout(total=60.0)
                )
    
    async def close_all(self):
        """Close all sessions"""
        async with self._lock:
            for session in self._sessions:
                await session.close()
            self._sessions.clear()
            self._session_created_times.clear()

# Global connection pool
_connection_pool = ConnectionPool()
